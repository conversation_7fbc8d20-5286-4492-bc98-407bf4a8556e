<?php
/**
 * Storage Configuration Cleanup Script
 * This script removes duplicate entries and keeps only the correct configuration
 */

require_once 'vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection
$host = $_ENV['DB_HOST'];
$dbname = $_ENV['DB_DATABASE'];
$username = $_ENV['DB_USERNAME'];
$password = $_ENV['DB_PASSWORD'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== CLEANING UP DUPLICATE STORAGE RECORDS ===\n\n";
    
    // Start transaction
    $pdo->beginTransaction();
    
    // Delete all existing storage configuration records
    $stmt = $pdo->prepare("DELETE FROM business_settings WHERE `key` IN ('local_storage', '3rd_party_storage', 's3_credential')");
    $stmt->execute();
    $deletedCount = $stmt->rowCount();
    echo "Deleted $deletedCount duplicate records.\n";
    
    // Insert clean configuration
    $s3_credentials = [
        'key' => $_ENV['AWS_ACCESS_KEY_ID'],
        'secret' => $_ENV['AWS_SECRET_ACCESS_KEY'],
        'region' => $_ENV['AWS_DEFAULT_REGION'],
        'bucket' => 'turantapp',
        'url' => $_ENV['AWS_URL'],
        'end_point' => $_ENV['AWS_ENDPOINT']
    ];
    
    // Insert s3_credential
    $stmt = $pdo->prepare("INSERT INTO business_settings (`key`, `value`, `created_at`, `updated_at`) VALUES ('s3_credential', ?, NOW(), NOW())");
    $stmt->execute([json_encode($s3_credentials)]);
    echo "✅ S3 credentials configured.\n";
    
    // Insert 3rd_party_storage = 1 (enabled)
    $stmt = $pdo->prepare("INSERT INTO business_settings (`key`, `value`, `created_at`, `updated_at`) VALUES ('3rd_party_storage', '1', NOW(), NOW())");
    $stmt->execute();
    echo "✅ 3rd party storage enabled.\n";
    
    // Insert local_storage = 0 (disabled)
    $stmt = $pdo->prepare("INSERT INTO business_settings (`key`, `value`, `created_at`, `updated_at`) VALUES ('local_storage', '0', NOW(), NOW())");
    $stmt->execute();
    echo "✅ Local storage disabled.\n";
    
    // Commit transaction
    $pdo->commit();
    
    echo "\n=== VERIFICATION ===\n";
    
    // Verify the cleanup
    $stmt = $pdo->prepare("SELECT `key`, `value` FROM business_settings WHERE `key` IN ('local_storage', '3rd_party_storage', 's3_credential') ORDER BY `key`");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Current Database Settings (after cleanup):\n";
    echo "-----------------------------------------\n";
    
    foreach ($results as $row) {
        echo "Key: " . $row['key'] . "\n";
        echo "Value: " . $row['value'] . "\n\n";
    }
    
    echo "✅ Cleanup completed successfully!\n";
    echo "Now try refreshing your admin panel and the toggle should work.\n";
    
} catch (PDOException $e) {
    // Rollback on error
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
