<?php
/**
 * Storage Setup Script for Cloudflare R2
 * Run this script to configure the storage settings in the database
 */

require_once 'vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection
$host = $_ENV['DB_HOST'];
$dbname = $_ENV['DB_DATABASE'];
$username = $_ENV['DB_USERNAME'];
$password = $_ENV['DB_PASSWORD'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n";
    
    // Set up S3 credentials
    $s3_credentials = [
        'key' => $_ENV['AWS_ACCESS_KEY_ID'],
        'secret' => $_ENV['AWS_SECRET_ACCESS_KEY'],
        'region' => $_ENV['AWS_DEFAULT_REGION'],
        'bucket' => 'turantapp',
        'url' => $_ENV['AWS_URL'],
        'end_point' => $_ENV['AWS_ENDPOINT']
    ];
    
    // Insert or update s3_credential setting
    $stmt = $pdo->prepare("
        INSERT INTO business_settings (`key`, `value`, `created_at`, `updated_at`)
        VALUES ('s3_credential', ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        `value` = VALUES(`value`),
        `updated_at` = NOW()
    ");
    $stmt->execute([json_encode($s3_credentials)]);
    echo "S3 credentials configured.\n";

    // Set 3rd party storage to enabled (1)
    $stmt = $pdo->prepare("
        INSERT INTO business_settings (`key`, `value`, `created_at`, `updated_at`)
        VALUES ('3rd_party_storage', '1', NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        `value` = '1',
        `updated_at` = NOW()
    ");
    $stmt->execute();
    echo "3rd party storage enabled.\n";

    // Set local storage to disabled (0)
    $stmt = $pdo->prepare("
        INSERT INTO business_settings (`key`, `value`, `created_at`, `updated_at`)
        VALUES ('local_storage', '0', NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        `value` = '0',
        `updated_at` = NOW()
    ");
    $stmt->execute();
    echo "Local storage disabled.\n";
    
    echo "\nStorage configuration completed successfully!\n";
    echo "You can now use the admin panel to toggle storage settings.\n";
    
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
