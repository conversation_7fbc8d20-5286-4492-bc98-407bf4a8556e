<?php
/**
 * Storage Configuration Diagnostic Script
 * This script checks the current storage configuration in the database
 */

require_once 'vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection
$host = $_ENV['DB_HOST'];
$dbname = $_ENV['DB_DATABASE'];
$username = $_ENV['DB_USERNAME'];
$password = $_ENV['DB_PASSWORD'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== STORAGE CONFIGURATION DIAGNOSTIC ===\n\n";
    
    // Check current storage settings
    $stmt = $pdo->prepare("SELECT `key`, `value` FROM business_settings WHERE `key` IN ('local_storage', '3rd_party_storage', 's3_credential') ORDER BY `key`");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Current Database Settings:\n";
    echo "-------------------------\n";
    
    foreach ($results as $row) {
        echo "Key: " . $row['key'] . "\n";
        echo "Value: " . $row['value'] . "\n";
        
        if ($row['key'] === 's3_credential') {
            $credentials = json_decode($row['value'], true);
            if ($credentials) {
                echo "  Parsed S3 Credentials:\n";
                foreach ($credentials as $k => $v) {
                    echo "    $k: $v\n";
                }
            }
        }
        echo "\n";
    }
    
    // Check if records exist
    if (empty($results)) {
        echo "❌ No storage configuration found in database!\n";
        echo "Running setup again...\n\n";
        
        // Re-run setup
        $s3_credentials = [
            'key' => $_ENV['AWS_ACCESS_KEY_ID'],
            'secret' => $_ENV['AWS_SECRET_ACCESS_KEY'],
            'region' => $_ENV['AWS_DEFAULT_REGION'],
            'bucket' => 'turantapp',
            'url' => $_ENV['AWS_URL'],
            'end_point' => $_ENV['AWS_ENDPOINT']
        ];
        
        // Insert records
        $stmt = $pdo->prepare("INSERT INTO business_settings (`key`, `value`, `created_at`, `updated_at`) VALUES ('s3_credential', ?, NOW(), NOW()) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`), `updated_at` = NOW()");
        $stmt->execute([json_encode($s3_credentials)]);
        
        $stmt = $pdo->prepare("INSERT INTO business_settings (`key`, `value`, `created_at`, `updated_at`) VALUES ('3rd_party_storage', '1', NOW(), NOW()) ON DUPLICATE KEY UPDATE `value` = '1', `updated_at` = NOW()");
        $stmt->execute();
        
        $stmt = $pdo->prepare("INSERT INTO business_settings (`key`, `value`, `created_at`, `updated_at`) VALUES ('local_storage', '0', NOW(), NOW()) ON DUPLICATE KEY UPDATE `value` = '0', `updated_at` = NOW()");
        $stmt->execute();
        
        echo "✅ Configuration re-applied!\n";
    } else {
        echo "✅ Storage configuration found in database.\n";
    }
    
    // Environment variables check
    echo "\nEnvironment Variables:\n";
    echo "---------------------\n";
    echo "FILESYSTEM_DRIVER: " . ($_ENV['FILESYSTEM_DRIVER'] ?? 'NOT SET') . "\n";
    echo "AWS_ACCESS_KEY_ID: " . (isset($_ENV['AWS_ACCESS_KEY_ID']) ? 'SET (' . strlen($_ENV['AWS_ACCESS_KEY_ID']) . ' chars)' : 'NOT SET') . "\n";
    echo "AWS_SECRET_ACCESS_KEY: " . (isset($_ENV['AWS_SECRET_ACCESS_KEY']) ? 'SET (' . strlen($_ENV['AWS_SECRET_ACCESS_KEY']) . ' chars)' : 'NOT SET') . "\n";
    echo "AWS_DEFAULT_REGION: " . ($_ENV['AWS_DEFAULT_REGION'] ?? 'NOT SET') . "\n";
    echo "AWS_BUCKET: " . ($_ENV['AWS_BUCKET'] ?? 'NOT SET') . "\n";
    echo "AWS_URL: " . ($_ENV['AWS_URL'] ?? 'NOT SET') . "\n";
    echo "AWS_ENDPOINT: " . ($_ENV['AWS_ENDPOINT'] ?? 'NOT SET') . "\n";
    
    echo "\n=== DIAGNOSTIC COMPLETE ===\n";
    
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
